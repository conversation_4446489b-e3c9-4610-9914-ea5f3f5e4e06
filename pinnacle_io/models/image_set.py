"""
SQLAlchemy model for Pinnacle ImageSet data.

This module provides the ImageSet data model for representing medical image sets (CT, MR, etc.) in Pinnacle,
including all image-specific parameters, relationships to image info, and associated patient data.
"""

from __future__ import annotations
from typing import Optional, List, Tuple, TYPE_CHECKING
import warnings

import numpy as np
from sqlalchemy import Column, String, Integer, Float, ForeignKey, LargeBinary
from sqlalchemy.orm import Mapped, relationship

from pinnacle_io.models.pinnacle_base import PinnacleBase
from pinnacle_io.models.types import JsonList

# Use TYPE_CHECKING to avoid circular imports
if TYPE_CHECKING:
    from pinnacle_io.models.image_info import ImageInfo
    from pinnacle_io.models.plan import Plan
    from pinnacle_io.models.patient import Patient


class ImageSet(PinnacleBase):
    """
    Model representing a medical image set (CT, MR, PET, etc.) in Pinnacle treatment planning.

    This class serves as the comprehensive container for medical imaging data used in
    radiation therapy treatment planning. It handles DICOM-compliant image sets with
    full support for 3D/4D volumetric data, coordinate system transformations, and
    pixel data management. The ImageSet model bridges the gap between DICOM imaging
    standards and Pinnacle's internal data representation.

    The model supports multiple imaging modalities including CT (computed tomography),
    MR (magnetic resonance), PET (positron emission tomography), and other medical
    imaging formats. It provides robust handling of large binary pixel data, spatial
    coordinate systems, and temporal sequences for 4D imaging applications.

    Technical Details:
        - Full DICOM compliance with Series and Study UID management
        - Efficient binary storage of large pixel data arrays (up to GB scale)
        - Support for multiple coordinate systems (patient, DICOM, scanner)
        - Automatic numpy array conversion for pixel data manipulation
        - Optimized relationship loading for performance with large datasets
        - Comprehensive metadata preservation from original DICOM files
        - Support for gated and 4D imaging sequences

    DICOM Integration Features:
        - Series Instance UID and Study Instance UID tracking
        - Image Orientation Patient (IOP) and Image Position Patient (IPP) support
        - Frame of Reference UID for spatial registration
        - DICOM file name preservation for traceability
        - Acquisition time and temporal sequence management
        - SUV scaling for PET imaging quantification
        - Color LUT scaling for specialized imaging modalities

    Coordinate System Support:
        - Patient coordinate system (LPS: Left-Posterior-Superior)
        - DICOM coordinate system with proper orientation matrices
        - Scanner coordinate system for acquisition parameters
        - Automatic coordinate transformations between systems
        - Couch position and table height tracking
        - Isocenter and treatment planning coordinate alignment

    Use Cases:
        - Primary CT image sets for treatment planning
        - Secondary imaging (MR, PET) for target delineation
        - 4D CT for respiratory motion management
        - CBCT for image-guided radiation therapy
        - Multi-modality image registration and fusion
        - Dose calculation grid definition
        - Quality assurance and verification imaging

    Attributes:
        # Core Identification
        id (int): Primary key identifier
        image_set_id (int): Pinnacle-specific image set identifier (not primary key)
        image_name (str): Human-readable name for the image set
        name_from_scanner (str): Original name from imaging scanner

        # DICOM Identifiers and Metadata
        series_uid (str): DICOM Series Instance UID for unique identification
        study_uid (str): DICOM Study Instance UID linking related series
        series_number (int): DICOM series number within study
        acquisition_number (int): DICOM acquisition number for temporal ordering
        series_uid_text (str): Text representation of series UID
        series_description (str): DICOM series description from scanner

        # Study and Patient Information
        study_id (str): Study identifier from imaging system
        exam_id (str): Examination identifier
        mrn (str): Medical record number
        dob (str): Date of birth (DICOM format)
        medical_record (str): Medical record information

        # Imaging Modality and Acquisition
        modality (str): Primary imaging modality ("CT", "MR", "PET", "RTIMAGE")
        modality_type (str): Specific modality subtype or protocol
        manufacturer (str): Scanner manufacturer (e.g., "Siemens", "GE", "Philips")
        model (str): Scanner model name
        station_name (str): Imaging station identifier
        scanner_id (str): Unique scanner identifier

        # Image Dimensions and Geometry
        x_dim (int): Number of pixels in X (left-right) dimension
        y_dim (int): Number of pixels in Y (anterior-posterior) dimension
        z_dim (int): Number of slices in Z (superior-inferior) dimension
        t_dim (int): Number of temporal phases (for 4D imaging)
        number_of_images (int): Total number of image slices

        # Pixel Spacing and Physical Dimensions
        x_pixdim (float): Pixel spacing in X direction (mm)
        y_pixdim (float): Pixel spacing in Y direction (mm)
        z_pixdim (float): Slice thickness/spacing in Z direction (mm)
        t_pixdim (float): Temporal resolution for 4D sequences (ms)

        # Spatial Origin and Positioning
        x_start (float): X coordinate of image origin (mm)
        y_start (float): Y coordinate of image origin (mm)
        z_start (float): Z coordinate of image origin (mm)
        t_start (float): Temporal start time for 4D sequences
        z_time (float): Z-direction temporal offset
        x_start_dicom (float): DICOM X coordinate origin
        y_start_dicom (float): DICOM Y coordinate origin

        # Patient and Couch Positioning
        patient_position (str): Patient position code ("HFS", "HFP", "FFS", "FFP")
        couch_pos (float): Couch longitudinal position (mm)
        couch_height (float): Couch vertical height (mm)
        has_couchheight (int): Flag indicating couch height availability (0/1)
        x_offset (float): X-axis offset from isocenter (mm)
        y_offset (float): Y-axis offset from isocenter (mm)

        # Image Orientation and Coordinate System
        image_orientation_patient (list): DICOM Image Orientation Patient [6 values]
        image_position_patient (list): DICOM Image Position Patient [3 values]
        orientation (int): Pinnacle orientation code

        # Pixel Data and Storage
        pixel_data (bytes): Raw pixel data stored as binary array
        datatype (int): Pixel data type identifier
        bitpix (int): Bits per pixel
        bytes_pix (int): Bytes per pixel
        vol_max (float): Maximum pixel value in volume
        vol_min (float): Minimum pixel value in volume
        byte_order (int): Byte order for pixel data (endianness)

        # Data Processing and Conversion
        read_conversion (str): Conversion formula for reading pixel values
        write_conversion (str): Conversion formula for writing pixel values
        dim_units (str): Units for dimensional measurements
        voxel_type (str): Voxel data type specification
        vol_type (str): Volume type classification

        # Acquisition Parameters
        scan_time_from_scanner (str): Original scan timestamp from scanner
        series_date_time (str): DICOM series date and time
        acquisition_time (str): DICOM acquisition time
        image_time (str): DICOM image time
        date (str): General date field
        kvp (str): X-ray tube voltage (kVp) for CT
        exposure (float): Radiation exposure parameters
        scan_options (str): Scanner-specific acquisition options
        scan_acquisition (int): Acquisition sequence identifier

        # File and Storage Management
        file_name (str): Original file name or path
        fname_format (str): File naming format specification
        fname_index_start (int): Starting index for file naming
        fname_index_delta (int): Index increment for file naming
        binary_header_size (int): Size of binary header in bytes
        db_name (str): Database name for storage

        # Specialized Imaging Features
        gating_uid (str): UID for respiratory/cardiac gating
        gating_type (str): Type of gating applied ("RESPIRATORY", "CARDIAC")
        gating_value (str): Gating threshold or trigger value
        irradiation_event_uid (str): UID for radiation delivery events

        # Quality and Processing Flags
        vis_only (int): Visualization-only flag (0/1)
        dataset_modified (int): Dataset modification flag (0/1)
        is_eeov (int): Enhanced edge-of-volume flag (0/1)
        is_omar (int): Orthopedic metal artifact reduction flag (0/1)
        low_sag (str): Low sagging correction parameters
        negative_voxel (str): Negative voxel handling specification
        binning_type (str): Pixel binning type applied

        # Advanced Imaging Parameters
        image_diameter (float): Effective imaging diameter (mm)
        version (str): Image format or protocol version
        originator (str): System or user that created the image set
        comment (str): Free-text comments or annotations

    Relationships:
        patient (Patient): Parent patient record (many-to-one)
            - Foreign key: patient_id
            - Back reference: patient.image_set_list

        image_info_list (List[ImageInfo]): Per-slice metadata and DICOM information
            - Cascade: all, delete-orphan (automatic cleanup)
            - Lazy: selectin (optimized loading)
            - Contains: slice-specific DICOM tags, positions, timing

        plan_list (List[Plan]): Treatment plans using this image set as primary CT
            - Foreign key: Plan.primary_ct_image_set_id
            - Lazy: selectin (optimized loading)
            - Use for: Treatment planning, dose calculation

    Example:
        >>> # Create a new CT image set
        >>> ct_images = ImageSet(
        ...     image_name="Planning CT",
        ...     modality="CT",
        ...     series_uid="1.2.840.113619.2.55.3.604688119.868.**********.123",
        ...     x_dim=512, y_dim=512, z_dim=120,
        ...     x_pixdim=0.976, y_pixdim=0.976, z_pixdim=2.5,
        ...     patient_position="HFS"
        ... )
        >>>
        >>> # Access image properties
        >>> print(f"Image: {ct_images.image_name}")
        >>> print(f"Dimensions: {ct_images.get_image_dimensions()}")
        >>> print(f"Pixel spacing: {ct_images.get_pixel_spacing()}")
        >>>
        >>> # Work with pixel data
        >>> slice_data = ct_images.get_slice_data(60)  # Get middle slice
        >>> if slice_data is not None:
        ...     print(f"Slice shape: {slice_data.shape}")
    """

    __tablename__ = "ImageSet"

    # Identification and basic info
    series_uid: Mapped[Optional[str]] = Column("SeriesUID", String, nullable=True)
    study_uid: Mapped[Optional[str]] = Column("StudyUID", String, nullable=True)
    series_number: Mapped[Optional[int]] = Column("SeriesNumber", Integer, nullable=True)
    acquisition_number: Mapped[Optional[int]] = Column("AcquisitionNumber", Integer, nullable=True)

    # Image information
    image_set_id: Mapped[Optional[int]] = Column("ImageSetID", Integer, nullable=True)  # From the Patient file. Not the primary key.
    image_name: Mapped[Optional[str]] = Column("ImageName", String, nullable=True)
    name_from_scanner: Mapped[Optional[str]] = Column("NameFromScanner", String, nullable=True)
    exam_id: Mapped[Optional[str]] = Column("ExamID", String, nullable=True)
    study_id: Mapped[Optional[str]] = Column("StudyID", String, nullable=True)
    modality: Mapped[Optional[str]] = Column("Modality", String, nullable=True)  # CT, MR, etc.
    modality_type: Mapped[Optional[str]] = Column("ModalityType", String, nullable=True)
    number_of_images: Mapped[Optional[int]] = Column(
        "NumberOfImages", Integer, nullable=True
    )
    scan_time_from_scanner: Mapped[Optional[str]] = Column(
        "ScanTimeFromScanner", String, nullable=True
    )
    file_name: Mapped[Optional[str]] = Column("FileName", String, nullable=True)
    series_description: Mapped[Optional[str]] = Column(
        "SeriesDescription", String, nullable=True
    )
    mrn: Mapped[Optional[str]] = Column("MRN", String, nullable=True)
    dob: Mapped[Optional[str]] = Column("DOB", String, nullable=True)
    gating_uid: Mapped[Optional[str]] = Column("GatingUID", String, nullable=True)
    series_uid_text: Mapped[Optional[str]] = Column(
        "SeriesUIDText", String, nullable=True
    )

    # Dimensions
    x_dim: Mapped[Optional[int]] = Column("XDim", Integer, nullable=True)
    y_dim: Mapped[Optional[int]] = Column("YDim", Integer, nullable=True)
    z_dim: Mapped[Optional[int]] = Column("ZDim", Integer, nullable=True)
    t_dim: Mapped[Optional[int]] = Column("TDim", Integer, nullable=True)

    # Additional pixel dimensions from header
    t_pixdim: Mapped[Optional[float]] = Column("TPixDim", Float, nullable=True)
    x_pixdim: Mapped[Optional[float]] = Column("XPixDim", Float, nullable=True)
    y_pixdim: Mapped[Optional[float]] = Column("YPixDim", Float, nullable=True)
    z_pixdim: Mapped[Optional[float]] = Column("ZPixDim", Float, nullable=True)

    # Start positions
    t_start: Mapped[Optional[float]] = Column("TStart", Float, nullable=True)
    x_start: Mapped[Optional[float]] = Column("XStart", Float, nullable=True)
    y_start: Mapped[Optional[float]] = Column("YStart", Float, nullable=True)
    z_start: Mapped[Optional[float]] = Column("ZStart", Float, nullable=True)
    z_time: Mapped[Optional[float]] = Column("ZTime", Float, nullable=True)
    x_start_dicom: Mapped[Optional[float]] = Column("XStartDicom", Float, nullable=True)
    y_start_dicom: Mapped[Optional[float]] = Column("YStartDicom", Float, nullable=True)

    # Data type information
    datatype: Mapped[Optional[int]] = Column("Datatype", Integer, nullable=True)
    bitpix: Mapped[Optional[int]] = Column("Bitpix", Integer, nullable=True)
    bytes_pix: Mapped[Optional[int]] = Column("BytesPix", Integer, nullable=True)
    vol_max: Mapped[Optional[float]] = Column("VolMax", Float, nullable=True)
    vol_min: Mapped[Optional[float]] = Column("VolMin", Float, nullable=True)

    # Additional fields from header
    byte_order: Mapped[Optional[int]] = Column("ByteOrder", Integer, nullable=True)
    read_conversion: Mapped[Optional[str]] = Column(
        "ReadConversion", String, nullable=True
    )
    write_conversion: Mapped[Optional[str]] = Column(
        "WriteConversion", String, nullable=True
    )
    dim_units: Mapped[Optional[str]] = Column("DimUnits", String, nullable=True)
    voxel_type: Mapped[Optional[str]] = Column("VoxelType", String, nullable=True)
    vis_only: Mapped[Optional[int]] = Column("VisOnly", Integer, nullable=True)
    # data_type: Mapped[Optional[str]] = Column("DataType", String, nullable=True) # See datatype above
    vol_type: Mapped[Optional[str]] = Column("VolType", String, nullable=True)
    db_name: Mapped[Optional[str]] = Column("DBName", String, nullable=True)
    medical_record: Mapped[Optional[str]] = Column(
        "MedicalRecord", String, nullable=True
    )
    originator: Mapped[Optional[str]] = Column("Originator", String, nullable=True)
    date: Mapped[Optional[str]] = Column("Date", String, nullable=True)
    scanner_id: Mapped[Optional[str]] = Column("ScannerID", String, nullable=True)
    patient_position: Mapped[Optional[str]] = Column(
        "PatientPosition", String, nullable=True
    )
    orientation: Mapped[Optional[int]] = Column("Orientation", Integer, nullable=True)
    scan_acquisition: Mapped[Optional[int]] = Column(
        "ScanAcquisition", Integer, nullable=True
    )
    comment: Mapped[Optional[str]] = Column("Comment", String, nullable=True)
    fname_format: Mapped[Optional[str]] = Column("FnameFormat", String, nullable=True)
    fname_index_start: Mapped[Optional[int]] = Column(
        "FnameIndexStart", Integer, nullable=True
    )
    fname_index_delta: Mapped[Optional[int]] = Column(
        "FnameIndexDelta", Integer, nullable=True
    )
    binary_header_size: Mapped[Optional[int]] = Column(
        "BinaryHeaderSize", Integer, nullable=True
    )
    manufacturer: Mapped[Optional[str]] = Column("Manufacturer", String, nullable=True)
    model: Mapped[Optional[str]] = Column("Model", String, nullable=True)
    couch_pos: Mapped[Optional[float]] = Column("CouchPos", Float, nullable=True)
    couch_height: Mapped[Optional[float]] = Column("CouchHeight", Float, nullable=True)
    x_offset: Mapped[Optional[float]] = Column("XOffset", Float, nullable=True)
    y_offset: Mapped[Optional[float]] = Column("YOffset", Float, nullable=True)
    dataset_modified: Mapped[Optional[int]] = Column(
        "DatasetModified", Integer, nullable=True
    )
    gating_type: Mapped[Optional[str]] = Column("GatingType", String, nullable=True)
    gating_value: Mapped[Optional[str]] = Column("GatingValue", String, nullable=True)
    irradiation_event_uid: Mapped[Optional[str]] = Column(
        "IrradiationEventUID", String, nullable=True
    )
    scan_options: Mapped[Optional[str]] = Column("ScanOptions", String, nullable=True)
    low_sag: Mapped[Optional[str]] = Column("LowSag", String, nullable=True)
    negative_voxel: Mapped[Optional[str]] = Column(
        "NegativeVoxel", String, nullable=True
    )
    station_name: Mapped[Optional[str]] = Column("StationName", String, nullable=True)
    kvp: Mapped[Optional[str]] = Column("KVP", String, nullable=True)
    exposure: Mapped[Optional[float]] = Column("Exposure", Float, nullable=True)
    series_date_time: Mapped[Optional[str]] = Column(
        "SeriesDateTime", String, nullable=True
    )
    version: Mapped[Optional[str]] = Column("Version", String, nullable=True)
    binning_type: Mapped[Optional[str]] = Column("BinningType", String, nullable=True)
    is_eeov: Mapped[Optional[int]] = Column("IsEEOV", Integer, nullable=True)
    is_omar: Mapped[Optional[int]] = Column("IsOMAR", Integer, nullable=True)
    image_diameter: Mapped[Optional[float]] = Column(
        "ImageDiameter", Float, nullable=True
    )
    has_couchheight: Mapped[Optional[int]] = Column(
        "HasCouchHeight", Integer, nullable=True
    )

    # Image orientation
    image_orientation_patient: Mapped[list] = Column(
        "ImageOrientationPatient", JsonList, default=[1, 0, 0, 0, 1, 0]
    )
    image_position_patient: Mapped[list] = Column(
        "ImagePositionPatient", JsonList, default=[0, 0, 0]
    )

    # Pixel data - stored separately as binary data
    pixel_data: Mapped[bytes] = Column("PixelData", LargeBinary, nullable=True)

    # Relationships
    patient_id: Mapped[int] = Column("PatientID", Integer, ForeignKey("Patient.ID"))
    patient: Mapped["Patient"] = relationship(
        "Patient",
        back_populates="image_set_list",
        lazy="joined"  # Optimize loading as patient info is frequently needed
    )

    image_info_list: Mapped[List["ImageInfo"]] = relationship(
        "ImageInfo",
        back_populates="image_set",
        cascade="all, delete-orphan",
        lazy="selectin"  # Use selectin loading for better performance with collections
    )

    plan_list: Mapped[List["Plan"]] = relationship(
        "Plan",
        back_populates="primary_ct_image_set",
        foreign_keys="Plan.primary_ct_image_set_id",
        primaryjoin="ImageSet.id == Plan.primary_ct_image_set_id",
        lazy="selectin"  # Use selectin loading for better performance with collections
    )

    def __init__(self, pixel_data: Optional[np.ndarray] = None, **kwargs):
        """
        Initialize an ImageSet instance.

        Args:
            pixel_data: Numpy array of pixel data to store in the database
            **kwargs: Keyword arguments used to initialize ImageSet attributes

        Relationships:
            image_info_list (List[ImageInfo]): List of ImageInfo objects associated with this ImageSet (one-to-many).
            plan (Plan): The parent Plan to which this ImageSet belongs (many-to-one), if applicable.
        """
        # Handle pixel_data if it's a numpy array
        if pixel_data is None:
            pixel_data = kwargs.pop("pixel_data", kwargs.pop("PixelData", None))
        if pixel_data is not None:
            try:
                if isinstance(pixel_data, np.ndarray):
                    kwargs["pixel_data"] = pixel_data.tobytes()
                else:
                    warnings.warn(
                        f"pixel_data is not a numpy array (pixel_data={pixel_data!r})",
                        stacklevel=2,
                    )
            except ImportError:
                warnings.warn(
                    f"Could not convert pixel_data to a byte array (pixel_data={pixel_data!r})",
                    stacklevel=2,
                )

        # Initialize the model with the remaining kwargs
        super().__init__(**kwargs)

    def __repr__(self) -> str:
        """Return a string representation of the ImageSet instance."""
        return f"<ImageSet(id={self.id}, name='{self.image_name}', modality='{self.modality}')>"

    @property
    def table_positions(self) -> List[float]:
        """
        Get the table positions for all image slices in this image set.

        Returns:
            List[float]: A list of table positions in millimeters for each image slice.
                        The positions correspond to the physical location of the patient table
                        during image acquisition for each slice.

        Example:
            >>> image_set = ImageSet()
            >>> positions = image_set.table_positions
            >>> print(f"Table moved from {min(positions)} to {max(positions)} mm")
        """
        return [image_info.table_position for image_info in self.image_info_list]

    @property
    def couch_positions(self) -> List[float]:
        """
        Get the couch positions for all image slices in this image set.

        Returns:
            List[float]: A list of couch positions in millimeters for each image slice.
                        These represent the longitudinal position of the patient couch
                        during image acquisition.

        Example:
            >>> image_set = ImageSet()
            >>> positions = image_set.couch_positions
            >>> print(f"Couch range: {max(positions) - min(positions)} mm")
        """
        return [image_info.couch_pos for image_info in self.image_info_list]

    @property
    def slice_numbers(self) -> List[int]:
        """
        Get the slice numbers for all images in this image set.

        Returns:
            List[int]: A list of slice numbers indicating the sequential order
                      of images in the acquisition. Typically starts from 1.

        Example:
            >>> image_set = ImageSet()
            >>> slices = image_set.slice_numbers
            >>> print(f"Image set contains {len(slices)} slices")
        """
        return [image_info.slice_number for image_info in self.image_info_list]

    @property
    def study_instance_uid(self) -> str:
        """
        Get the DICOM Study Instance UID for this image set.

        Returns:
            str: The Study Instance UID from the first image in the set,
                 or empty string if no images are present. This UID uniquely
                 identifies the imaging study across DICOM systems.

        Example:
            >>> image_set = ImageSet()
            >>> study_uid = image_set.study_instance_uid
            >>> print(f"Study UID: {study_uid}")
        """
        return (
            self.image_info_list[0].study_instance_uid if self.image_info_list else ""
        )

    @property
    def frame_of_reference_uid(self) -> str:
        """
        Get the DICOM Frame of Reference UID for this image set.

        Returns:
            str: The Frame of Reference UID from the first image in the set,
                 or empty string if no images are present. This UID defines
                 the coordinate system for spatial relationships between images.

        Example:
            >>> image_set = ImageSet()
            >>> frame_uid = image_set.frame_of_reference_uid
            >>> print(f"Frame of Reference: {frame_uid}")
        """
        return self.image_info_list[0].frame_uid if self.image_info_list else ""

    @property
    def class_uid(self) -> str:
        """
        Get the DICOM SOP Class UID for this image set.

        Returns:
            str: The SOP Class UID from the first image in the set,
                 or empty string if no images are present. This UID identifies
                 the type of DICOM object (e.g., CT Image Storage).

        Example:
            >>> image_set = ImageSet()
            >>> class_uid = image_set.class_uid
            >>> print(f"DICOM Class: {class_uid}")
        """
        return self.image_info_list[0].class_uid if self.image_info_list else ""

    @property
    def instance_uids(self) -> List[str]:
        """
        Get the DICOM SOP Instance UIDs for all images in this image set.

        Returns:
            List[str]: A list of SOP Instance UIDs, one for each image slice.
                      Each UID uniquely identifies a specific DICOM instance
                      within the study and series.

        Example:
            >>> image_set = ImageSet()
            >>> uids = image_set.instance_uids
            >>> print(f"Image set contains {len(uids)} unique instances")
        """
        return [image_info.instance_uid for image_info in self.image_info_list]

    @property
    def suv_scales(self) -> List[float]:
        """
        Get the Standardized Uptake Value (SUV) scaling factors for PET images.

        Returns:
            List[float]: A list of SUV scaling factors for each image slice.
                        Used to convert pixel values to SUV units in PET imaging.
                        Returns empty values for non-PET modalities.

        Example:
            >>> pet_image_set = ImageSet(modality="PT")
            >>> suv_factors = pet_image_set.suv_scales
            >>> print(f"SUV scaling range: {min(suv_factors)} to {max(suv_factors)}")
        """
        return [image_info.suv_scale for image_info in self.image_info_list]

    @property
    def color_lut_scales(self) -> List[float]:
        """
        Get the color lookup table scaling factors for each image slice.

        Returns:
            List[float]: A list of color LUT scaling factors used for
                        image display and windowing. These factors control
                        how pixel values are mapped to display intensities.

        Example:
            >>> image_set = ImageSet()
            >>> lut_scales = image_set.color_lut_scales
            >>> print(f"Display scaling factors: {lut_scales}")
        """
        return [image_info.color_lut_scale for image_info in self.image_info_list]

    @property
    def dicom_file_names(self) -> List[str]:
        """
        Get the original DICOM file names for all images in this image set.

        Returns:
            List[str]: A list of DICOM file names as they existed during
                      import. Useful for tracing back to original data sources
                      and maintaining audit trails.

        Example:
            >>> image_set = ImageSet()
            >>> file_names = image_set.dicom_file_names
            >>> print(f"First file: {file_names[0] if file_names else 'None'}")
        """
        return [image_info.dicom_file_name for image_info in self.image_info_list]

    @property
    def acquisition_times(self) -> List[str]:
        """
        Get the DICOM acquisition times for all images in this image set.

        Returns:
            List[str]: A list of acquisition times in DICOM time format (HHMMSS.FFFFFF).
                      Represents the time when each image slice was acquired
                      during the imaging procedure.

        Example:
            >>> image_set = ImageSet()
            >>> acq_times = image_set.acquisition_times
            >>> print(f"Acquisition started at: {acq_times[0] if acq_times else 'Unknown'}")
        """
        return [image_info.acquisition_time for image_info in self.image_info_list]

    @property
    def image_times(self) -> List[str]:
        """
        Get the DICOM image times for all images in this image set.

        Returns:
            List[str]: A list of image times in DICOM time format (HHMMSS.FFFFFF).
                      Represents the time when each image was created or processed,
                      which may differ from acquisition time.

        Example:
            >>> image_set = ImageSet()
            >>> img_times = image_set.image_times
            >>> print(f"Images processed at: {img_times}")
        """
        return [image_info.image_time for image_info in self.image_info_list]

    def get_image_dimensions(self) -> Tuple[int, int, int]:
        """
        Get the image dimensions.

        Returns:
            Tuple of (x_dim, y_dim, z_dim).
        """
        return (self.x_dim, self.y_dim, self.z_dim)

    def get_pixel_spacing(self) -> Tuple[float, float, float]:
        """
        Get the pixel spacing.

        Returns:
            Tuple of (x_pixdim, y_pixdim, z_pixdim) in mm.
        """
        return (self.x_pixdim, self.y_pixdim, self.z_pixdim)

    def get_slice_data(self, slice_index: int) -> Optional["np.ndarray"]:
        """
        Get the pixel data for a specific slice.

        Args:
            slice_index: Index of the slice to retrieve.

        Returns:
            2D numpy array of pixel data for the specified slice, or None if pixel data is not available.
        """
        try:
            import numpy as np

            if self.pixel_data is None or slice_index >= self.z_dim:
                return None

            # Convert bytes back to numpy array
            array_data = np.frombuffer(self.pixel_data, dtype=np.int16)
            reshaped_data = array_data.reshape((self.x_dim, self.y_dim, self.z_dim))

            return reshaped_data[:, :, slice_index]
        except ImportError:
            return None

    def set_slice_data(self, slice_index: int, data: "np.ndarray") -> None:
        """
        Set the pixel data for a specific slice.

        Args:
            slice_index: Index of the slice to set.
            data: 2D numpy array of pixel data for the slice.
        """
        try:
            import numpy as np

            # Convert bytes to numpy array if pixel_data exists
            if self.pixel_data is not None:
                array_data = np.frombuffer(self.pixel_data, dtype=np.int16)
                reshaped_data = array_data.reshape((self.x_dim, self.y_dim, self.z_dim))
            else:
                # Initialize pixel data array if it doesn't exist
                reshaped_data = np.zeros(
                    (self.x_dim, self.y_dim, self.z_dim), dtype=np.int16
                )

            if slice_index < self.z_dim:
                reshaped_data[:, :, slice_index] = data

            # Store back as bytes
            self.pixel_data = reshaped_data.tobytes()
        except ImportError:
            pass
