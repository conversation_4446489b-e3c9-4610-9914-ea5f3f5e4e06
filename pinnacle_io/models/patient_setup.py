"""
SQLAlchemy model for Pinnacle PatientPosition data.

This module provides the PatientPosition model for representing patient position and setup information
in Pinnacle treatment plans. The model handles both static position information and coordinate
transformations between different reference systems (Pinnacle vs DICOM).

Key Capabilities:
- Stores patient position and orientation information (supine/prone, head-first/feet-first)
- Manages coordinate transformations between Pinnacle and DICOM coordinate systems
- Provides transformation matrices for different patient setups
- Handles image orientation data
- Supports patient setup validation and error handling

The module is essential for ensuring accurate spatial relationships in radiation therapy planning,
particularly when data needs to be exchanged between different systems or coordinate frames.
"""

from typing import Optional, List, TYPE_CHECKING
import numpy as np
import json

from sqlalchemy import Column, String, Integer, ForeignKey
from sqlalchemy.orm import relationship, Mapped

from pinnacle_io.models.versioned_base import VersionedBase
from pinnacle_io.utils.patient_enum import (
    PatientPositionEnum,
    PatientOrientationEnum,
    TableMotionEnum,
    PatientSetupEnum,
)

if TYPE_CHECKING:
    from pinnacle_io.models.plan import Plan


class PatientSetup(VersionedBase):
    """
    Model representing patient positioning and coordinate transformation parameters for treatment planning.

    This class stores comprehensive patient positioning information including orientation, position,
    and table motion parameters, along with coordinate transformation matrices for converting
    between Pinnacle and DICOM coordinate systems. It serves as the essential bridge for spatial
    data consistency across different radiation therapy planning and delivery systems.

    The PatientSetup model is critical for ensuring accurate spatial relationships in radiation
    therapy, particularly when treatment data needs to be exchanged between different systems
    or when coordinate transformations are required for dose calculations, contour processing,
    or treatment delivery verification.

    Attributes:
        id (int): Primary key inherited from VersionedBase
        position (str): Patient position (e.g., "Supine", "Prone")
        orientation (str): Patient orientation (e.g., "HeadFirst", "FeetFirst")
        table_motion (str): Table motion direction (e.g., "IntoScanner", "OutOfScanner")
        patient_setup (str): Combined patient setup designation (e.g., "HFS", "HFP", "FFS", "FFP")
        pinnacle_to_dicom_matrix (str): JSON-serialized 4x4 transformation matrix from Pinnacle to DICOM coordinates
        dicom_to_pinnacle_matrix (str): JSON-serialized 4x4 transformation matrix from DICOM to Pinnacle coordinates
        image_orientation_patient (str): DICOM Image Orientation Patient values as comma-separated string
        patient_position_description (str): Human-readable description of patient position
        patient_position_enum (str): Enumerated patient position value
        plan_id (int): Foreign key to the parent Plan

    Relationships:
        plan (Plan): Parent plan that owns this patient setup (many-to-one)

    Database Mapping:
        This model maps to the "PatientSetup" table in the Pinnacle database schema.
        Column names follow Pinnacle's PascalCase convention for database compatibility.

    Coordinate Systems:
        The model handles transformations between two primary coordinate systems:

        1. **Pinnacle Coordinate System**: Used internally by Pinnacle for treatment planning
        2. **DICOM Coordinate System**: Standard medical imaging coordinate system

        Transformation matrices are automatically initialized based on patient setup:
        - **HFS (Head First Supine)**: Standard supine positioning
        - **HFP (Head First Prone)**: Prone positioning with head toward gantry
        - **FFS (Feet First Supine)**: Supine with feet toward gantry
        - **FFP (Feet First Prone)**: Prone with feet toward gantry

    Usage:
        PatientSetup instances are typically created automatically when loading Plan data
        from Pinnacle files. They can also be created manually for coordinate transformation
        tasks or when setting up new treatment plans.

        Example:
            >>> patient_setup = PatientSetup(
            ...     position="Supine",
            ...     orientation="HeadFirst",
            ...     table_motion="IntoScanner"
            ... )
            >>> # Transform a point from Pinnacle to DICOM coordinates
            >>> dicom_point = patient_setup.transform_point_pinnacle_to_dicom([10.0, 20.0, 30.0])

    Clinical Significance:
        Accurate patient positioning and coordinate transformations are essential for:
        - Precise dose delivery during treatment
        - Accurate contour and structure positioning
        - Proper image registration and fusion
        - Quality assurance and treatment verification
        - Data exchange between different treatment planning systems
    """

    __tablename__ = "PatientSetup"

    # Primary key is inherited from VersionedBase

    # These fields come from the plan.PatientSetup file
    position: Mapped[Optional[str]] = Column(
        "Position", String, nullable=True
    )
    orientation: Mapped[Optional[str]] = Column(
        "Orientation", String, nullable=True
    )
    table_motion: Mapped[Optional[str]] = Column(
        "TableMotion", String, nullable=True
    )

    # Patient setup is derived from the fields above
    patient_setup: Mapped[Optional[str]] = Column(
        "PatientSetup", String, nullable=True
    )

    # Transformation matrices stored as serialized strings
    # These matrices transform coordinates from one system to another
    pinnacle_to_dicom_matrix: Mapped[Optional[str]] = Column(
        "PinnacleToDicomMatrix", String, nullable=True
    )
    dicom_to_pinnacle_matrix: Mapped[Optional[str]] = Column(
        "DicomToPinnacleMatrix", String, nullable=True
    )

    # Image orientation stored as serialized array
    image_orientation_patient: Mapped[Optional[str]] = Column(
        "ImageOrientationPatient", String, nullable=True
    )

    # Patient position description and enum
    patient_position_description: Mapped[Optional[str]] = Column(
        "PatientPositionDescription", String, nullable=True
    )
    patient_position_enum: Mapped[Optional[str]] = Column(
        "PatientPositionEnum", String, nullable=True
    )

    # Parent relationship
    plan_id: Mapped[Optional[int]] = Column(
        "PlanID", Integer, ForeignKey("Plan.ID"), nullable=True
    )
    plan: Mapped[Optional["Plan"]] = relationship(
        "Plan",
        back_populates="_patient_position",
        lazy="joined"  # Optimize loading as plan info is frequently needed
    )

    # trial_id: Mapped[Optional[int]] = Column(
    #     "TrialID", Integer, ForeignKey("Trial.ID"), nullable=True
    # )
    # trial = relationship("Trial", back_populates="_patient_position")

    def __init__(self, **kwargs):
        """
        Initialize a PatientSetup instance.

        Args:
            **kwargs: Keyword arguments used to initialize PatientSetup attributes.
                Common attributes include:
                - position: Patient position (e.g., "Supine", "Prone")
                - orientation: Patient orientation (e.g., "HeadFirst", "FeetFirst")
                - table_motion: Table motion direction
                - patient_setup: Combined setup designation (auto-derived if not provided)
                - pinnacle_to_dicom_matrix: Transformation matrix (auto-initialized if not provided)
                - dicom_to_pinnacle_matrix: Inverse transformation matrix (auto-initialized if not provided)
                - image_orientation_patient: DICOM image orientation values
                - plan: Parent Plan object

        Relationships:
            plan (Plan): The parent Plan to which this PatientSetup belongs (many-to-one).

        Note:
            If patient_setup is not provided, it will be automatically derived from
            position and orientation parameters. Transformation matrices will be
            automatically initialized based on the patient setup if not explicitly provided.

        Example:
            >>> patient_setup = PatientSetup(
            ...     position="Supine",
            ...     orientation="HeadFirst",
            ...     table_motion="IntoScanner"
            ... )
        """
        # Convert enum fields to strings
        for field in [
            "position",
            "Position",
            "orientation",
            "Orientation",
            "table_motion",
            "TableMotion",
            "patient_setup",
            "PatientSetup",
        ]:
            if field in kwargs and not isinstance(kwargs[field], str):
                kwargs[field] = getattr(kwargs[field], "value", str(kwargs[field]))

        if "patient_setup" not in kwargs:
            kwargs["patient_setup"] = PatientSetupEnum.from_orientation_and_position(
                PatientOrientationEnum(
                    kwargs.get("orientation", kwargs.get("Orientation"))
                    or PatientOrientationEnum.Unknown.value
                ),
                PatientPositionEnum(
                    kwargs.get("position", kwargs.get("Position"))
                    or PatientPositionEnum.Unknown.value
                ),
            ).value

        # Handle numpy array serialization
        if "pinnacle_to_dicom_matrix" in kwargs and isinstance(
            kwargs["pinnacle_to_dicom_matrix"], np.ndarray
        ):
            kwargs["pinnacle_to_dicom_matrix"] = json.dumps(
                kwargs["pinnacle_to_dicom_matrix"].tolist()
            )

        if "dicom_to_pinnacle_matrix" in kwargs and isinstance(
            kwargs["dicom_to_pinnacle_matrix"], np.ndarray
        ):
            kwargs["dicom_to_pinnacle_matrix"] = json.dumps(
                kwargs["dicom_to_pinnacle_matrix"].tolist()
            )

        if "image_orientation_patient" in kwargs and isinstance(
            kwargs["image_orientation_patient"], list
        ):
            kwargs["image_orientation_patient"] = ",".join(
                str(float(x)) for x in kwargs["image_orientation_patient"]
            )

        super().__init__(**kwargs)

        # Initialize transformation matrices if not provided
        if (
            "pinnacle_to_dicom_matrix" not in kwargs
            and "dicom_to_pinnacle_matrix" not in kwargs
        ):
            self._initialize_transformation_matrices()

    def __repr__(self) -> str:
        """
        Return a string representation of this patient setup.

        Returns:
            str: A string containing the patient setup's ID, setup designation, and position info.
        """
        return f"<PatientSetup(id={self.id}, setup='{self.patient_setup}', position='{self.position}', orientation='{self.orientation}')>"

    @property
    def position_enum(self) -> PatientPositionEnum:  # type: ignore
        """
        Get the position as an enum.

        Returns:
            Position as a PatientPositionEnum.
        """
        try:
            return PatientPositionEnum(self.position)
        except ValueError:
            return PatientPositionEnum.Unknown

    @property
    def orientation_enum(self) -> PatientOrientationEnum:  # type: ignore
        """
        Get the orientation as an enum.

        Returns:
            Orientation as a PatientOrientationEnum.
        """
        try:
            return PatientOrientationEnum(self.orientation)
        except ValueError:
            return PatientOrientationEnum.Unknown

    @property
    def table_motion_enum(self) -> TableMotionEnum:  # type: ignore
        """
        Get the table motion as an enum.

        Returns:
            Table motion as a TableMotionEnum.
        """
        try:
            return TableMotionEnum(self.table_motion)
        except ValueError:
            return TableMotionEnum.Unknown

    @property
    def patient_setup_enum(self) -> PatientSetupEnum:  # type: ignore
        """
        Get the patient setup as an enum.

        Returns:
            Patient setup as a PatientSetupEnum.
        """
        try:
            return PatientSetupEnum(self.patient_setup)
        except ValueError:
            return PatientSetupEnum.Unknown

    @property
    def pinnacle_to_dicom_matrix_array(self) -> np.ndarray:
        """
        Get the pinnacle to DICOM transformation matrix as a numpy array.

        Returns:
            Transformation matrix as a numpy array.
        """
        try:
            return np.array(json.loads(self.pinnacle_to_dicom_matrix))
        except (json.JSONDecodeError, ValueError):
            return np.eye(4)

    @property
    def dicom_to_pinnacle_matrix_array(self) -> np.ndarray:
        """
        Get the DICOM to pinnacle transformation matrix as a numpy array.

        Returns:
            Transformation matrix as a numpy array.
        """
        try:
            return np.array(json.loads(self.dicom_to_pinnacle_matrix))
        except (json.JSONDecodeError, ValueError):
            return np.eye(4)

    @property
    def image_orientation_patient_array(self) -> List[float]:
        """
        Get the image orientation patient as a list of floats.

        Returns:
            Image orientation patient as a list of floats.
        """
        try:
            return [float(x) for x in self.image_orientation_patient.split(",")]
        except ValueError:
            return [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]

    def _initialize_transformation_matrices(self):
        """
        Initialize transformation matrices based on position type.
        """
        if self.patient_setup == PatientSetupEnum.HFS:  # Head First Supine
            # Default transformations for different patient positions
            # For HFS and anything that isn't HFP, FFS, and FFP,
            # Pinnacle and DICOM coordinates are related as follows:
            # DICOM X = -Pinnacle X
            # DICOM Y = -Pinnacle Y
            # DICOM Z = Pinnacle Z
            self.pinnacle_to_dicom_matrix = json.dumps(
                [[-1, 0, 0, 0], [0, -1, 0, 0], [0, 0, 1, 0], [0, 0, 0, 1]]
            )

            # Inverse transformation
            self.dicom_to_pinnacle_matrix = json.dumps(
                [[-1, 0, 0, 0], [0, -1, 0, 0], [0, 0, 1, 0], [0, 0, 0, 1]]
            )

            # Image orientation for HFS
            self.image_orientation_patient = ",".join(str(float(x)) for x in [1, 0, 0, 0, 1, 0])

        elif self.patient_setup == PatientSetupEnum.HFP:  # Head First Prone
            # For HFP, Pinnacle and DICOM coordinates are related as follows:
            # DICOM X = Pinnacle X
            # DICOM Y = Pinnacle Y
            # DICOM Z = Pinnacle Z
            self.pinnacle_to_dicom_matrix = json.dumps(
                [[1, 0, 0, 0], [0, 1, 0, 0], [0, 0, 1, 0], [0, 0, 0, 1]]
            )

            # Inverse transformation
            self.dicom_to_pinnacle_matrix = json.dumps(
                [[1, 0, 0, 0], [0, 1, 0, 0], [0, 0, 1, 0], [0, 0, 0, 1]]
            )

            # Image orientation for HFP
            self.image_orientation_patient = ",".join(str(float(x)) for x in [-1, 0, 0, 0, -1, 0])

        elif self.patient_setup == PatientSetupEnum.FFS:  # Feet First Supine
            # For FFS, Pinnacle and DICOM coordinates are related as follows:
            # DICOM X = Pinnacle X
            # DICOM Y = -Pinnacle Y
            # DICOM Z = -Pinnacle Z
            self.pinnacle_to_dicom_matrix = json.dumps(
                [[1, 0, 0, 0], [0, -1, 0, 0], [0, 0, -1, 0], [0, 0, 0, 1]]
            )

            # Inverse transformation
            self.dicom_to_pinnacle_matrix = json.dumps(
                [[1, 0, 0, 0], [0, -1, 0, 0], [0, 0, -1, 0], [0, 0, 0, 1]]
            )

            # Image orientation for FFS
            self.image_orientation_patient = ",".join(str(float(x)) for x in [-1, 0, 0, 0, 1, 0])

        elif self.patient_setup == PatientSetupEnum.FFP:  # Feet First Prone
            # For FFP, Pinnacle and DICOM coordinates are related as follows:
            # DICOM X = -Pinnacle X
            # DICOM Y = Pinnacle Y
            # DICOM Z = -Pinnacle Z
            self.pinnacle_to_dicom_matrix = json.dumps(
                [[-1, 0, 0, 0], [0, 1, 0, 0], [0, 0, -1, 0], [0, 0, 0, 1]]
            )

            # Inverse transformation
            self.dicom_to_pinnacle_matrix = json.dumps(
                [[-1, 0, 0, 0], [0, 1, 0, 0], [0, 0, -1, 0], [0, 0, 0, 1]]
            )

            # Image orientation for FFP
            self.image_orientation_patient = ",".join(str(float(x)) for x in [1, 0, 0, 0, -1, 0])

    def transform_point_pinnacle_to_dicom(self, point: List[float]) -> List[float]:  # type: ignore
        """
        Transform a point from Pinnacle coordinates to DICOM coordinates.

        Args:
            point: Point in Pinnacle coordinates [x, y, z].

        Returns:
            Point in DICOM coordinates [x, y, z].
        """
        # Convert to homogeneous coordinates
        homogeneous_point = np.array([point[0], point[1], point[2], 1.0])

        # Apply transformation
        transformed_point = np.dot(self.pinnacle_to_dicom_matrix_array, homogeneous_point)

        # Convert back to 3D coordinates
        return [transformed_point[0], transformed_point[1], transformed_point[2]]

    def transform_point_dicom_to_pinnacle(self, point: List[float]) -> List[float]:  # type: ignore
        """
        Transform a point from DICOM coordinates to Pinnacle coordinates.

        Args:
            point: Point in DICOM coordinates [x, y, z].

        Returns:
            Point in Pinnacle coordinates [x, y, z].
        """
        # Convert to homogeneous coordinates
        homogeneous_point = np.array([point[0], point[1], point[2], 1.0])

        # Apply transformation
        transformed_point = np.dot(self.dicom_to_pinnacle_matrix_array, homogeneous_point)

        # Convert back to 3D coordinates
        return [transformed_point[0], transformed_point[1], transformed_point[2]]

    def transform_contour_pinnacle_to_dicom(
        self, contour_points: List[List[float]]
    ) -> List[List[float]]:  # type: ignore
        """
        Transform a list of contour points from Pinnacle coordinates to DICOM coordinates.

        Args:
            contour_points: List of points in Pinnacle coordinates [[x1, y1, z1], [x2, y2, z2], ...].

        Returns:
            List of points in DICOM coordinates [[x1, y1, z1], [x2, y2, z2], ...].
        """
        return [
            self.transform_point_pinnacle_to_dicom(point) for point in contour_points
        ]

    def transform_contour_dicom_to_pinnacle(
        self, contour_points: List[List[float]]
    ) -> List[List[float]]:  # type: ignore
        """
        Transform a list of contour points from DICOM coordinates to Pinnacle coordinates.

        Args:
            contour_points: List of points in DICOM coordinates [[x1, y1, z1], [x2, y2, z2], ...].

        Returns:
            List of points in Pinnacle coordinates [[x1, y1, z1], [x2, y2, z2], ...].
        """
        return [
            self.transform_point_dicom_to_pinnacle(point) for point in contour_points
        ]
