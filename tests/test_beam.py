"""
Tests for the Beam model.
"""

from pinnacle_io.models import (
    Beam,
    Trial,
    ControlPoint,
    Compensator,
    DoseEngine,
    MonitorUnitInfo,
    CPManager,
    Dose,
)


def test_beam_initialization():
    """Test creating a Beam directly with kwargs."""
    # Test with minimal data
    beam = Beam(
        name="Test Beam",
        beam_number=1,
        modality="Photon",
        machine_energy_name="6MV",
        weight=1.0,
    )

    assert beam.name == "Test Beam"
    assert beam.beam_number == 1
    assert beam.modality == "Photon"
    assert beam.machine_energy_name == "6MV"
    assert beam.weight == 1.0
    assert beam.monitor_units_valid is None # Not specified


def test_beam_with_trial_relationship():
    """Test Beam relationship with Trial."""
    # Create a trial
    trial = Trial(trial_id=1, trial_name="Test Trial")

    # Create a beam with relationship to trial
    beam = Beam(name="Test Beam", beam_number=1, trial=trial)

    # Verify trial relationship
    assert beam.trial is trial
    assert beam.trial_id == trial.id
    assert beam in trial.beam_list


def test_beam_with_control_points():
    """Test Beam relationship with ControlPoints."""
    # Create a beam
    beam = Beam(name="Test Beam", beam_number=1)

    # Create control points and add to beam
    cp1 = ControlPoint(
        control_point_number=0, gantry_angle=0.0, collimator_angle=0.0, beam=beam
    )

    cp2 = ControlPoint(
        control_point_number=1, gantry_angle=10.0, collimator_angle=5.0, beam=beam
    )

    # Verify control points relationship
    assert len(beam.control_point_list) == 2
    assert beam.control_point_list[0] is cp1
    assert beam.control_point_list[1] is cp2
    assert all(cp.beam is beam for cp in beam.control_point_list)


def test_beam_with_compensator():
    """Test Beam relationship with Compensator and Compensator attributes."""
    # Create a beam
    beam = Beam(name="Test Beam", beam_number=1)

    # Create compensator with various attributes and add to beam
    compensator = Compensator(
        beam=beam,
        name="Test Compensator",
        export_name="ExportComp",
        tray_number="T1",
        export_format="FormatA",
        is_valid=1,
        generated_automatically=0,
        width=10.5,
        height=5.2,
        density=2.7,
        type="Brass",
        min_allowable_thickness=0.1,
        max_allowable_thickness=5.0,
        dose_min=1.0,
        dose_max=2.0,
        dose_mean=1.5,
        dose_std_dev=0.2,
    )

    # Verify compensator relationship
    assert beam.compensator is compensator
    assert compensator.beam is beam

    # Verify compensator attributes
    assert compensator.name == "Test Compensator"
    assert compensator.export_name == "ExportComp"
    assert compensator.tray_number == "T1"
    assert compensator.export_format == "FormatA"
    assert compensator.is_valid == 1
    assert compensator.generated_automatically == 0
    assert compensator.width == 10.5
    assert compensator.height == 5.2
    assert compensator.density == 2.7
    assert compensator.type == "Brass"
    assert compensator.min_allowable_thickness == 0.1
    assert compensator.max_allowable_thickness == 5.0
    assert compensator.dose_min == 1.0
    assert compensator.dose_max == 2.0
    assert compensator.dose_mean == 1.5
    assert compensator.dose_std_dev == 0.2

    # Test __repr__
    repr_str = repr(compensator)
    assert "Compensator" in repr_str
    assert f"id={compensator.id}" in repr_str
    assert f"name='{compensator.name}'" in repr_str


def test_beam_with_dose_engine():
    """Test Beam relationship with DoseEngine."""
    # Create a beam
    beam = Beam(name="Test Beam", beam_number=1)

    # Create dose engine and add to beam
    dose_engine = DoseEngine(beam=beam)

    # Verify dose engine relationship
    assert beam.dose_engine is dose_engine
    assert dose_engine.beam is beam


def test_beam_with_monitor_unit_info():
    """Test Beam relationship with MonitorUnitInfo."""
    # Create a beam
    beam = Beam(name="Test Beam", beam_number=1)

    # Create monitor unit info and add to beam
    monitor_unit_info = MonitorUnitInfo(beam=beam)

    # Verify monitor unit info relationship
    assert beam.monitor_unit_info is monitor_unit_info
    assert monitor_unit_info.beam is beam


def test_beam_with_cp_manager():
    """Test Beam relationship with CPManager."""
    # Create a beam
    beam = Beam(name="Test Beam", beam_number=1)

    # Create CP manager and add to beam
    cp_manager = CPManager(beam=beam)

    # Verify CP manager relationship
    assert beam.cp_manager is cp_manager
    assert cp_manager.beam is beam


def test_beam_with_dose():
    """Test Beam relationship with Dose."""
    # Create a beam
    beam = Beam(name="Test Beam", beam_number=1)

    # Create dose and add to beam
    dose = Dose(beam=beam)

    # Verify dose relationship
    assert beam.dose is dose
    assert dose.beam is beam


def test_beam_with_all_relationships():
    """Test Beam with all relationships."""
    # Create a trial
    trial = Trial(trial_id=1, trial_name="Test Trial")

    # Create a beam with all relationships
    beam = Beam(name="Test Beam", beam_number=1, trial=trial)

    # Add control points
    ControlPoint(
        control_point_number=0, gantry_angle=0.0, collimator_angle=0.0, beam=beam
    )

    ControlPoint(
        control_point_number=1, gantry_angle=10.0, collimator_angle=5.0, beam=beam
    )

    # Add other relationships
    compensator = Compensator(beam=beam)
    dose_engine = DoseEngine(beam=beam)
    monitor_unit_info = MonitorUnitInfo(beam=beam)
    cp_manager = CPManager(beam=beam)
    dose = Dose(beam=beam)

    # Verify all relationships
    assert beam.trial is trial
    assert len(beam.control_point_list) == 2
    assert beam.compensator is compensator
    assert beam.dose_engine is dose_engine
    assert beam.monitor_unit_info is monitor_unit_info
    assert beam.cp_manager is cp_manager
    assert beam.dose is dose

    # Verify bidirectional relationships
    assert beam in trial.beam_list
    assert all(cp.beam is beam for cp in beam.control_point_list)
    assert compensator.beam is beam
    assert dose_engine.beam is beam
    assert monitor_unit_info.beam is beam
    assert cp_manager.beam is beam
    assert dose.beam is beam


def test_beam_attributes():
    """Test Beam attributes."""
    beam = Beam(
        name="Test Beam",
        beam_number=1,
        isocenter_name="ISO1",
        prescription_name="PRESC1",
        use_poi_for_prescription_point=1,
        prescription_point_name="POI1",
        prescription_point_depth=5.0,
        machine_name_and_version="LINAC_v1",
        modality="Photon",
        machine_energy_name="6MV",
        set_beam_type="Static",
        use_mlc=1,
        ssd=100.0,
        weight=1.0,
        field_id="FIELD1",
        dose_rate=600.0,
    )

    # Verify attributes
    assert beam.name == "Test Beam"
    assert beam.beam_number == 1
    assert beam.isocenter_name == "ISO1"
    assert beam.prescription_name == "PRESC1"
    assert beam.use_poi_for_prescription_point == 1
    assert beam.prescription_point_name == "POI1"
    assert beam.prescription_point_depth == 5.0
    assert beam.machine_name_and_version == "LINAC_v1"
    assert beam.modality == "Photon"
    assert beam.machine_energy_name == "6MV"
    assert beam.set_beam_type == "Static"
    assert beam.use_mlc == 1
    assert beam.ssd == 100.0
    assert beam.weight == 1.0
    assert beam.field_id == "FIELD1"
    assert beam.dose_rate == 600.0


def test_beam_repr():
    """Test the string representation of a Beam."""
    # Create a beam with a known ID, name, and beam_number
    beam = Beam(
        id=42,
        name="Test Beam",
        beam_number=3
    )

    # Verify the __repr__ output matches the expected format
    expected_repr = "<Beam(id=42, beam_number=3, name='Test Beam')>"
    assert repr(beam) == expected_repr


def test_beam_arc_detection():
    """Test beam arc detection methods."""
    # Create a beam with CP manager
    beam = Beam(name="Arc Beam", beam_number=1)
    cp_manager = CPManager(beam=beam)

    # Test with no control points - should not be arc
    assert not beam.is_arc()
    assert beam.is_cw is None
    assert beam.is_ccw is None

    # Test with single control point - should not be arc
    cp1 = ControlPoint(control_point_number=0, gantry=0.0, beam=beam, cp_manager=cp_manager)
    assert not beam.is_arc()
    assert beam.is_cw is None
    assert beam.is_ccw is None

    # Test with two control points with small gantry difference - should not be arc
    cp2 = ControlPoint(control_point_number=1, gantry=0.5, beam=beam, cp_manager=cp_manager)
    assert not beam.is_arc()
    assert beam.is_cw is None
    assert beam.is_ccw is None

    # Test with two control points with large gantry difference - should be arc
    cp2.gantry = 10.0
    assert beam.is_arc()

    # Test clockwise arc detection (need to set gantry_is_ccw first)
    cp_manager.gantry_is_ccw = 0  # 0 means clockwise
    assert beam.is_cw is True
    assert beam.is_ccw is False

    # Test counter-clockwise arc detection
    cp_manager.gantry_is_ccw = 1  # 1 means counter-clockwise
    assert beam.is_cw is False
    assert beam.is_ccw is True


def test_beam_feature_detection():
    """Test beam feature detection methods."""
    # Create a beam with CP manager
    beam = Beam(name="Feature Beam", beam_number=1)
    CPManager(beam=beam)

    # Test with no control points
    assert not beam.has_wedge()
    assert not beam.has_mlc()

    # Add control point without wedge or MLC
    cp = ControlPoint(control_point_number=0, gantry=0.0, beam=beam, cp_manager=beam.cp_manager)
    assert not beam.has_wedge()
    assert not beam.has_mlc()

    # Add wedge context
    from pinnacle_io.models.wedge_context import WedgeContext
    wedge_context = WedgeContext(
        wedge_id="W1",
        wedge_number=1,
        wedge_angle=30.0,
        wedge_name="30deg",
        control_point=cp
    )
    cp.wedge_context = wedge_context
    assert beam.has_wedge()

    # Add MLC positions
    from pinnacle_io.models.mlc import MLCLeafPositions
    import numpy as np

    # Create MLC positions with correct 60x2 array size
    mlc_data = np.zeros((60, 2))  # 60 leaf pairs, each with left and right positions
    mlc_data[:, 0] = -10.0  # Left leaf positions
    mlc_data[:, 1] = 10.0   # Right leaf positions

    mlc_positions = MLCLeafPositions(
        number_of_points=60,
        number_of_dimensions=2,
        points=mlc_data,
        control_point=cp
    )
    cp._mlc_leaf_positions = mlc_positions
    assert beam.has_mlc()


def test_beam_control_point_management():
    """Test beam control point management methods."""
    # Create a beam with CP manager
    beam = Beam(name="CP Beam", beam_number=1)
    cp_manager = CPManager(beam=beam)

    # Test adding control point
    cp = ControlPoint(control_point_number=0, gantry_angle=0.0)
    beam.add_control_point(cp)
    assert cp in cp_manager.control_point_list

    # Test getting control point by index
    cp.index = 0
    found_cp = beam.get_control_point(0)
    assert found_cp is cp

    # Test getting non-existent control point
    not_found = beam.get_control_point(999)
    assert not_found is None

    # Test with beam without CP manager
    beam_no_cp = Beam(name="No CP Beam", beam_number=2)
    beam_no_cp.add_control_point(cp)  # Should do nothing
    assert beam_no_cp.get_control_point(0) is None


def test_beam_monitor_units_computation():
    """Test beam monitor units computation."""
    # Create a beam with monitor unit info
    beam = Beam(name="MU Beam", beam_number=1)
    beam.monitor_unit_info = MonitorUnitInfo(
        beam=beam,
        prescription_dose=200.0,
        normalized_dose=1.0,
        collimator_output_factor=1.0,
        total_transmission_fraction=1.0
    )

    # Test successful computation
    pdd = 0.67  # 67% PDD at 10cm
    mu = beam.compute_monitor_units(pdd)
    expected_mu = 200.0 / (1.0 * 1.0 * 1.0 * 0.67)
    assert abs(mu - expected_mu) < 1e-6

    # Test with beam without monitor unit info
    beam_no_mu = Beam(name="No MU Beam", beam_number=2)
    try:
        beam_no_mu.compute_monitor_units(0.67)
        assert False, "Should raise ValueError"
    except ValueError as e:
        assert "Monitor unit information is not available" in str(e)


def test_beam_dose_volume_file_property():
    """Test beam dose volume file property."""
    # Create a beam with dose volume
    beam = Beam(name="Dose Volume Beam", beam_number=1, dose_volume="DoseVolume:001")

    # Test dose volume file property
    expected_file = "plan.Trial.binary.001"
    assert beam.dose_volume_file == expected_file

    # Test with different dose volume format
    beam.dose_volume = "DoseVolume:123"
    expected_file = "plan.Trial.binary.123"
    assert beam.dose_volume_file == expected_file


def test_beam_edge_cases():
    """Test beam edge cases and error handling."""
    # Test beam with None values
    beam = Beam()
    assert beam.name is None
    assert beam.beam_number is None
    assert beam.modality is None

    # Test beam with empty strings
    beam_empty = Beam(name="", modality="", machine_energy_name="")
    assert beam_empty.name == ""
    assert beam_empty.modality == ""
    assert beam_empty.machine_energy_name == ""

    # Test beam with very long strings
    long_name = "A" * 1000
    beam_long = Beam(name=long_name)
    assert beam_long.name == long_name

    # Test beam with special characters
    special_name = "Beam #1 (90°) - Test/Validation"
    beam_special = Beam(name=special_name)
    assert beam_special.name == special_name
