"""
Unit tests for the DoseGrid model.
"""
from pathlib import Path

from pinnacle_io.models import DoseGrid, Trial, Dose, VoxelSize, Dimension, Coordinate
from pinnacle_io.readers.trial_reader import TrialReader


def test_init_with_kwargs():
    """Test initializing a DoseGrid with keyword arguments."""
    dose_grid = DoseGrid(
        voxel_size_x=0.3,
        voxel_size_y=0.3, 
        voxel_size_z=0.3,
        dimension_x=93,
        dimension_y=110,
        dimension_z=89,
        origin_x=-14.9044,
        origin_y=-17.5729,
        origin_z=-10.7747,
        vol_rot_delta_x=0,
        vol_rot_delta_y=0,
        vol_rot_delta_z=0,
        display_2d=1,
        dose_summation_type=1
    )
    
    assert dose_grid.voxel_size_x == 0.3
    assert dose_grid.voxel_size_y == 0.3
    assert dose_grid.voxel_size_z == 0.3
    assert dose_grid.dimension_x == 93
    assert dose_grid.dimension_y == 110
    assert dose_grid.dimension_z == 89
    assert dose_grid.origin_x == -14.9044
    assert dose_grid.origin_y == -17.5729
    assert dose_grid.origin_z == -10.7747
    assert dose_grid.vol_rot_delta_x == 0
    assert dose_grid.vol_rot_delta_y == 0
    assert dose_grid.vol_rot_delta_z == 0
    assert dose_grid.display_2d == 1
    assert dose_grid.dose_summation_type == 1


def test_init_with_spatial_objects():
    """Test initializing a DoseGrid with spatial type objects."""
    dose_grid = DoseGrid(
        voxel_size=VoxelSize(0.3, 0.3, 0.3),
        dimension=Dimension(93, 110, 89),
        origin=Coordinate(-14.9044, -17.5729, -10.7747),
        vol_rot_delta=Coordinate(0, 0, 0),
        display_2d=1,
        dose_summation_type=1
    )
    
    assert dose_grid.voxel_size_x == 0.3
    assert dose_grid.voxel_size_y == 0.3
    assert dose_grid.voxel_size_z == 0.3
    assert dose_grid.dimension_x == 93
    assert dose_grid.dimension_y == 110
    assert dose_grid.dimension_z == 89
    assert dose_grid.origin_x == -14.9044
    assert dose_grid.origin_y == -17.5729
    assert dose_grid.origin_z == -10.7747
    assert dose_grid.vol_rot_delta_x == 0
    assert dose_grid.vol_rot_delta_y == 0
    assert dose_grid.vol_rot_delta_z == 0
    
    # Test the spatial type properties
    assert isinstance(dose_grid.voxel_size, VoxelSize)
    assert isinstance(dose_grid.dimension, Dimension)
    assert isinstance(dose_grid.origin, Coordinate)
    assert isinstance(dose_grid.vol_rot_delta, Coordinate)


def test_init_with_dict_objects():
    """Test initializing a DoseGrid with dictionary objects."""
    dose_grid = DoseGrid(
        voxel_size={"x": 0.3, "y": 0.3, "z": 0.3},
        dimension={"x": 93, "y": 110, "z": 89},
        origin={"x": -14.9044, "y": -17.5729, "z": -10.7747},
        vol_rot_delta={"x": 0, "y": 0, "z": 0},
        display_2d=1,
        dose_summation_type=1
    )
    
    assert dose_grid.voxel_size_x == 0.3
    assert dose_grid.voxel_size_y == 0.3
    assert dose_grid.voxel_size_z == 0.3
    assert dose_grid.dimension_x == 93
    assert dose_grid.dimension_y == 110
    assert dose_grid.dimension_z == 89
    assert dose_grid.origin_x == -14.9044
    assert dose_grid.origin_y == -17.5729
    assert dose_grid.origin_z == -10.7747
    assert dose_grid.vol_rot_delta_x == 0
    assert dose_grid.vol_rot_delta_y == 0
    assert dose_grid.vol_rot_delta_z == 0


def test_init_with_pascal_case_dict_objects():
    """Test initializing a DoseGrid with PascalCase dictionary objects."""
    dose_grid = DoseGrid(
        VoxelSize={"X": 0.3, "Y": 0.3, "Z": 0.3},
        Dimension={"X": 93, "Y": 110, "Z": 89},
        Origin={"X": -14.9044, "Y": -17.5729, "Z": -10.7747},
        VolRotDelta={"X": 0, "Y": 0, "Z": 0},
        display_2d=1,
        dose_summation_type=1
    )
    
    assert dose_grid.voxel_size_x == 0.3
    assert dose_grid.voxel_size_y == 0.3
    assert dose_grid.voxel_size_z == 0.3
    assert dose_grid.dimension_x == 93
    assert dose_grid.dimension_y == 110
    assert dose_grid.dimension_z == 89
    assert dose_grid.origin_x == -14.9044
    assert dose_grid.origin_y == -17.5729
    assert dose_grid.origin_z == -10.7747
    assert dose_grid.vol_rot_delta_x == 0
    assert dose_grid.vol_rot_delta_y == 0
    assert dose_grid.vol_rot_delta_z == 0


def test_spatial_properties():
    """Test that spatial properties return the correct spatial type objects."""
    dose_grid = DoseGrid(
        voxel_size_x=0.3,
        voxel_size_y=0.3,
        voxel_size_z=0.3,
        dimension_x=93,
        dimension_y=110,
        dimension_z=89,
        origin_x=-14.9044,
        origin_y=-17.5729,
        origin_z=-10.7747,
        vol_rot_delta_x=0,
        vol_rot_delta_y=0,
        vol_rot_delta_z=0,
    )
    
    # Test voxel_size property
    voxel_size = dose_grid.voxel_size
    assert isinstance(voxel_size, VoxelSize)
    assert voxel_size.x == 0.3
    assert voxel_size.y == 0.3
    assert voxel_size.z == 0.3
    
    # Test dimension property
    dimension = dose_grid.dimension
    assert isinstance(dimension, Dimension)
    assert dimension.x == 93
    assert dimension.y == 110
    assert dimension.z == 89
    
    # Test origin property
    origin = dose_grid.origin
    assert isinstance(origin, Coordinate)
    assert origin.x == -14.9044
    assert origin.y == -17.5729
    assert origin.z == -10.7747
    
    # Test vol_rot_delta property
    vol_rot_delta = dose_grid.vol_rot_delta
    assert isinstance(vol_rot_delta, Coordinate)
    assert vol_rot_delta.x == 0
    assert vol_rot_delta.y == 0
    assert vol_rot_delta.z == 0


def test_relationship_with_trial():
    """Test the relationship between DoseGrid and Trial."""
    trial = Trial(trial_id=1, trial_name="Test Trial")
    dose_grid = DoseGrid(trial=trial)
    
    assert dose_grid.trial == trial
    assert dose_grid.trial_id == trial.id
    assert trial.dose_grid == dose_grid


def test_relationship_with_doses():
    """Test the relationship between DoseGrid and Dose."""
    dose_grid = DoseGrid()
    
    # Create some test doses
    dose1 = Dose(dose_id="Dose1", dose_grid=dose_grid)
    dose2 = Dose(dose_id="Dose2", dose_grid=dose_grid)
    
    # Check the relationship
    assert len(dose_grid.dose_list) == 2
    assert dose_grid.dose_list[0] == dose1
    assert dose_grid.dose_list[1] == dose2
    assert dose1.dose_grid == dose_grid
    assert dose2.dose_grid == dose_grid
    assert dose1.dose_grid_id == dose_grid.id
    assert dose2.dose_grid_id == dose_grid.id


def test_from_plan_trial_file():
    """Test loading DoseGrid data from a plan.Trial file."""
    plan_path = Path(__file__).parent / 'test_data/01/Institution_1/Mount_0/Patient_1/Plan_0'
    trials = TrialReader.read(str(plan_path))
    
    # Verify the first trial has a dose grid with expected values
    assert len(trials) > 0
    trial = trials[0]
    
    assert hasattr(trial, 'dose_grid')
    dose_grid = trial.dose_grid
    
    # Check that values match those in the plan.Trial file
    assert dose_grid.voxel_size_x == 0.3
    assert dose_grid.voxel_size_y == 0.3
    assert dose_grid.voxel_size_z == 0.3
    assert dose_grid.dimension_x == 93
    assert dose_grid.dimension_y == 110
    assert dose_grid.dimension_z == 89
    assert dose_grid.origin_x == -14.9044
    assert dose_grid.origin_y == -17.5729
    assert dose_grid.origin_z == -10.7747
    assert dose_grid.vol_rot_delta_x == 0
    assert dose_grid.vol_rot_delta_y == 0
    assert dose_grid.vol_rot_delta_z == 0
    assert dose_grid.display_2d == 1
    assert dose_grid.dose_summation_type == 1


def test_partial_vector_initialization():
    """Test initializing with partial vector data."""
    # Test with some vector components missing
    dose_grid = DoseGrid(
        voxel_size={"x": 0.3, "y": 0.3},  # Missing z
        dimension=Dimension(93, 110, 89), # 0 and None throw errors
        origin_x=-14.9044,  # Individual components
        origin_y=-17.5729,
        vol_rot_delta=None  # None for entire vector
    )
    
    assert dose_grid.voxel_size_x == 0.3
    assert dose_grid.voxel_size_y == 0.3
    assert dose_grid.voxel_size_z is None  # Not set
    assert dose_grid.dimension_x == 93
    assert dose_grid.dimension_y == 110
    assert dose_grid.dimension_z == 89
    assert dose_grid.origin_x == -14.9044
    assert dose_grid.origin_y == -17.5729
    assert dose_grid.origin_z is None  # Not set
    assert dose_grid.vol_rot_delta_x is None  # Not set
    assert dose_grid.vol_rot_delta_y is None  # Not set
    assert dose_grid.vol_rot_delta_z is None  # Not set


def test_complete_relationship_chain():
    """Test the complete relationship chain from Trial to DoseGrid to Dose."""
    # Create a trial
    trial = Trial(trial_id=1, trial_name="Test Trial")
    
    # Create a dose grid linked to the trial
    dose_grid = DoseGrid(trial=trial)
    
    # Create doses linked to the dose grid
    dose1 = Dose(dose_id="Dose1", dose_grid=dose_grid)
    dose2 = Dose(dose_id="Dose2", dose_grid=dose_grid)
    
    # Link a dose directly to the trial (plan dose)
    trial_dose = Dose(dose_id="TrialDose", trial=trial)
    
    # Verify relationships
    assert trial.dose_grid == dose_grid
    assert dose_grid.trial == trial
    assert dose1 in dose_grid.dose_list
    assert dose2 in dose_grid.dose_list
    assert dose1.dose_grid == dose_grid
    assert dose2.dose_grid == dose_grid
    assert trial.dose == trial_dose
    assert trial_dose.trial == trial


def test_mixed_case_parameters():
    """Test initializing a DoseGrid with a mix of snake_case and PascalCase parameters."""
    dose_grid = DoseGrid(
        voxel_size_x=0.3,  # snake_case individual component
        VoxelSize={"Y": 0.3},  # PascalCase dict with PascalCase key
        dimension={"z": 89},  # snake_case dict with snake_case key
        Origin={"X": -14.9044, "y": -17.5729},  # PascalCase dict with mixed keys
        vol_rot_delta_z=0  # snake_case individual component
    )
    
    assert dose_grid.voxel_size_x == 0.3
    assert dose_grid.voxel_size_y == 0.3
    assert dose_grid.voxel_size_z is None # Not set
    assert dose_grid.dimension_x is None  # Not set
    assert dose_grid.dimension_y is None  # Not set
    assert dose_grid.dimension_z == 89
    assert dose_grid.origin_x == -14.9044
    assert dose_grid.origin_y == -17.5729
    assert dose_grid.origin_z is None  # Not set
    assert dose_grid.vol_rot_delta_x is None  # Not set
    assert dose_grid.vol_rot_delta_y is None  # Not set
    assert dose_grid.vol_rot_delta_z == 0


def test_empty_vector_parameters():
    """Test initializing a DoseGrid with empty vector parameters."""
    dose_grid = DoseGrid(
        voxel_size={},  # Empty dict
        dimension=Dimension(1, 1, 1),  # Non-zero vector
        origin=None,  # None
        vol_rot_delta={}  # Empty dict
    )
    
    # Check that default values are used
    assert dose_grid.voxel_size_x is None  # Not set by empty dict
    assert dose_grid.voxel_size_y is None  # Not set by empty dict
    assert dose_grid.voxel_size_z is None  # Not set by empty dict
    assert dose_grid.dimension_x == 1
    assert dose_grid.dimension_y == 1
    assert dose_grid.dimension_z == 1
    assert dose_grid.origin_x is None  # Not set by None
    assert dose_grid.origin_y is None  # Not set by None
    assert dose_grid.origin_z is None  # Not set by None
    assert dose_grid.vol_rot_delta_x is None  # Not set by empty dict
    assert dose_grid.vol_rot_delta_y is None  # Not set by empty dict
    assert dose_grid.vol_rot_delta_z is None  # Not set by empty dict


def test_dose_grid_repr():
    """Test the string representation of a DoseGrid with different combinations of trial and dimension."""
    # Test with trial and dimension
    trial = Trial(trial_id=1, name="Test Trial")
    dose_grid = DoseGrid(
        id=42,
        trial=trial,
        dimension=Dimension(93, 110, 89)
    )
    expected_repr = "<DoseGrid(id=42, trial='Test Trial', dimension=(93, 110, 89))>"
    assert repr(dose_grid) == expected_repr

    # Test with trial but no dimension
    dose_grid = DoseGrid(id=42, trial=trial)
    expected_repr = "<DoseGrid(id=42, trial='Test Trial', dimension=(0, 0, 0))>"
    assert repr(dose_grid) == expected_repr

    # Test with dimension but no trial
    dose_grid = DoseGrid(
        id=42,
        dimension=Dimension(93, 110, 89)
    )
    expected_repr = "<DoseGrid(id=42, trial='', dimension=(93, 110, 89))>"
    assert repr(dose_grid) == expected_repr

    # Test with neither trial nor dimension
    dose_grid = DoseGrid(id=42)
    expected_repr = "<DoseGrid(id=42, trial='', dimension=(0, 0, 0))>"
    assert repr(dose_grid) == expected_repr


def test_voxel_size_setter():
    """Test the voxel_size property setter with various input types."""
    dose_grid = DoseGrid()

    # Test setting with VoxelSize object
    voxel_size_obj = VoxelSize(1.0, 2.0, 3.0)
    dose_grid.voxel_size = voxel_size_obj
    assert dose_grid.voxel_size_x == 1.0
    assert dose_grid.voxel_size_y == 2.0
    assert dose_grid.voxel_size_z == 3.0

    # Test setting with dictionary (lowercase keys)
    dose_grid.voxel_size = {'x': 2.5, 'y': 2.5, 'z': 5.0}
    assert dose_grid.voxel_size_x == 2.5
    assert dose_grid.voxel_size_y == 2.5
    assert dose_grid.voxel_size_z == 5.0

    # Test setting with dictionary (uppercase keys)
    dose_grid.voxel_size = {'X': 3.0, 'Y': 3.0, 'Z': 6.0}
    assert dose_grid.voxel_size_x == 3.0
    assert dose_grid.voxel_size_y == 3.0
    assert dose_grid.voxel_size_z == 6.0

    # Test setting with None
    dose_grid.voxel_size = None
    assert dose_grid.voxel_size_x is None
    assert dose_grid.voxel_size_y is None
    assert dose_grid.voxel_size_z is None

    # Test setting with object having x, y, z attributes
    class MockSpatial:
        def __init__(self, x, y, z):
            self.x, self.y, self.z = x, y, z

    mock_obj = MockSpatial(4.0, 5.0, 6.0)
    dose_grid.voxel_size = mock_obj
    assert dose_grid.voxel_size_x == 4.0
    assert dose_grid.voxel_size_y == 5.0
    assert dose_grid.voxel_size_z == 6.0


def test_dimension_setter():
    """Test the dimension property setter with various input types."""
    dose_grid = DoseGrid()

    # Test setting with Dimension object
    dim_obj = Dimension(128, 256, 64)
    dose_grid.dimension = dim_obj
    assert dose_grid.dimension_x == 128
    assert dose_grid.dimension_y == 256
    assert dose_grid.dimension_z == 64

    # Test setting with dictionary (lowercase keys)
    dose_grid.dimension = {'x': 64, 'y': 128, 'z': 32}
    assert dose_grid.dimension_x == 64
    assert dose_grid.dimension_y == 128
    assert dose_grid.dimension_z == 32

    # Test setting with dictionary (uppercase keys)
    dose_grid.dimension = {'X': 100, 'Y': 200, 'Z': 50}
    assert dose_grid.dimension_x == 100
    assert dose_grid.dimension_y == 200
    assert dose_grid.dimension_z == 50

    # Test setting with None
    dose_grid.dimension = None
    assert dose_grid.dimension_x is None
    assert dose_grid.dimension_y is None
    assert dose_grid.dimension_z is None

    # Test setting with object having x, y, z attributes
    class MockDimension:
        def __init__(self, x, y, z):
            self.x, self.y, self.z = x, y, z

    mock_dim = MockDimension(75, 150, 25)
    dose_grid.dimension = mock_dim
    assert dose_grid.dimension_x == 75
    assert dose_grid.dimension_y == 150
    assert dose_grid.dimension_z == 25


def test_origin_setter():
    """Test the origin property setter with various input types."""
    dose_grid = DoseGrid()

    # Test setting with Coordinate object
    coord_obj = Coordinate(-100.0, -50.0, 25.0)
    dose_grid.origin = coord_obj
    assert dose_grid.origin_x == -100.0
    assert dose_grid.origin_y == -50.0
    assert dose_grid.origin_z == 25.0

    # Test setting with dictionary (lowercase keys)
    dose_grid.origin = {'x': -200.0, 'y': -100.0, 'z': 50.0}
    assert dose_grid.origin_x == -200.0
    assert dose_grid.origin_y == -100.0
    assert dose_grid.origin_z == 50.0

    # Test setting with dictionary (uppercase keys)
    dose_grid.origin = {'X': -150.0, 'Y': -75.0, 'Z': 37.5}
    assert dose_grid.origin_x == -150.0
    assert dose_grid.origin_y == -75.0
    assert dose_grid.origin_z == 37.5

    # Test setting with None
    dose_grid.origin = None
    assert dose_grid.origin_x is None
    assert dose_grid.origin_y is None
    assert dose_grid.origin_z is None

    # Test setting with object having x, y, z attributes
    class MockCoordinate:
        def __init__(self, x, y, z):
            self.x, self.y, self.z = x, y, z

    mock_coord = MockCoordinate(-300.0, -150.0, 75.0)
    dose_grid.origin = mock_coord
    assert dose_grid.origin_x == -300.0
    assert dose_grid.origin_y == -150.0
    assert dose_grid.origin_z == 75.0


def test_vol_rot_delta_setter():
    """Test the vol_rot_delta property setter with various input types."""
    dose_grid = DoseGrid()

    # Test setting with Coordinate object
    coord_obj = Coordinate(0.1, 0.2, 0.3)
    dose_grid.vol_rot_delta = coord_obj
    assert dose_grid.vol_rot_delta_x == 0.1
    assert dose_grid.vol_rot_delta_y == 0.2
    assert dose_grid.vol_rot_delta_z == 0.3

    # Test setting with dictionary (lowercase keys)
    dose_grid.vol_rot_delta = {'x': 0.5, 'y': 0.6, 'z': 0.7}
    assert dose_grid.vol_rot_delta_x == 0.5
    assert dose_grid.vol_rot_delta_y == 0.6
    assert dose_grid.vol_rot_delta_z == 0.7

    # Test setting with dictionary (uppercase keys)
    dose_grid.vol_rot_delta = {'X': 0.8, 'Y': 0.9, 'Z': 1.0}
    assert dose_grid.vol_rot_delta_x == 0.8
    assert dose_grid.vol_rot_delta_y == 0.9
    assert dose_grid.vol_rot_delta_z == 1.0

    # Test setting with None
    dose_grid.vol_rot_delta = None
    assert dose_grid.vol_rot_delta_x is None
    assert dose_grid.vol_rot_delta_y is None
    assert dose_grid.vol_rot_delta_z is None

    # Test setting with object having x, y, z attributes
    class MockRotation:
        def __init__(self, x, y, z):
            self.x, self.y, self.z = x, y, z

    mock_rot = MockRotation(1.1, 1.2, 1.3)
    dose_grid.vol_rot_delta = mock_rot
    assert dose_grid.vol_rot_delta_x == 1.1
    assert dose_grid.vol_rot_delta_y == 1.2
    assert dose_grid.vol_rot_delta_z == 1.3


def test_get_voxel_volume():
    """Test the get_voxel_volume method."""
    dose_grid = DoseGrid()

    # Test with all voxel sizes set
    dose_grid.voxel_size_x = 2.0
    dose_grid.voxel_size_y = 3.0
    dose_grid.voxel_size_z = 4.0

    volume = dose_grid.get_voxel_volume()
    assert volume == 24.0  # 2.0 * 3.0 * 4.0

    # Test with one voxel size missing
    dose_grid.voxel_size_z = None
    volume = dose_grid.get_voxel_volume()
    assert volume is None

    # Test with all voxel sizes missing
    dose_grid.voxel_size_x = None
    dose_grid.voxel_size_y = None
    volume = dose_grid.get_voxel_volume()
    assert volume is None

    # Test with zero voxel size
    dose_grid.voxel_size_x = 0.0
    dose_grid.voxel_size_y = 2.0
    dose_grid.voxel_size_z = 3.0
    volume = dose_grid.get_voxel_volume()
    assert volume == 0.0


# Note: Skipping get_total_volume and get_grid_extent tests for now due to potential issues
# These methods may have dependencies that cause test hangs


def test_setter_error_handling():
    """Test error handling in property setters."""
    import pytest

    dose_grid = DoseGrid()

    # Test voxel_size setter with invalid input
    with pytest.raises(ValueError, match="Invalid voxel size value"):
        dose_grid.voxel_size = "invalid_string"

    # Test dimension setter with invalid input (wrapped in ValueError)
    with pytest.raises(ValueError, match="Invalid dimension value"):
        dose_grid.dimension = "invalid_string"

    # Test dimension setter with invalid dict values (non-numeric)
    with pytest.raises(ValueError):
        dose_grid.dimension = {'x': 'not_a_number', 'y': 100, 'z': 50}


def test_extract_xyz_helper_method():
    """Test the _extract_xyz helper method."""
    dose_grid = DoseGrid()

    # Test with VoxelSize object
    voxel_size = VoxelSize(1.0, 2.0, 3.0)
    result = dose_grid._extract_xyz(voxel_size)
    assert result == {'x': 1.0, 'y': 2.0, 'z': 3.0}

    # Test with dictionary (lowercase)
    result = dose_grid._extract_xyz({'x': 4.0, 'y': 5.0, 'z': 6.0})
    assert result == {'x': 4.0, 'y': 5.0, 'z': 6.0}

    # Test with dictionary (uppercase)
    result = dose_grid._extract_xyz({'X': 7.0, 'Y': 8.0, 'Z': 9.0})
    assert result == {'x': 7.0, 'y': 8.0, 'z': 9.0}

    # Test with mixed case dictionary
    result = dose_grid._extract_xyz({'x': 1.0, 'Y': 2.0, 'z': 3.0})
    assert result == {'x': 1.0, 'y': 2.0, 'z': 3.0}

    # Test with None and defaults
    result = dose_grid._extract_xyz(None, default_x=10.0, default_y=20.0, default_z=30.0)
    assert result == {'x': 10.0, 'y': 20.0, 'z': 30.0}

    # Test with None and no defaults
    result = dose_grid._extract_xyz(None)
    assert result == {'x': None, 'y': None, 'z': None}

    # Test with empty dictionary
    result = dose_grid._extract_xyz({})
    assert result == {'x': None, 'y': None, 'z': None}

    # Test with partial dictionary
    result = dose_grid._extract_xyz({'x': 1.0, 'z': 3.0})
    assert result == {'x': 1.0, 'y': None, 'z': 3.0}

    # Test with object having x, y, z attributes
    class MockObject:
        def __init__(self, x, y, z):
            self.x, self.y, self.z = x, y, z

    mock_obj = MockObject(11.0, 12.0, 13.0)
    result = dose_grid._extract_xyz(mock_obj)
    assert result == {'x': 11.0, 'y': 12.0, 'z': 13.0}


def test_property_getters_with_none_values():
    """Test property getters when some values are None."""
    dose_grid = DoseGrid()

    # Test voxel_size property with None values
    dose_grid.voxel_size_x = 1.0
    dose_grid.voxel_size_y = None
    dose_grid.voxel_size_z = 3.0

    voxel_size = dose_grid.voxel_size
    assert voxel_size is None  # Should return None if any component is None

    # Test dimension property with None values
    dose_grid.dimension_x = 100
    dose_grid.dimension_y = None
    dose_grid.dimension_z = 50

    dimension = dose_grid.dimension
    assert dimension is None  # Should return None if any component is None

    # Test origin property with None values
    dose_grid.origin_x = -100.0
    dose_grid.origin_y = None
    dose_grid.origin_z = 25.0

    origin = dose_grid.origin
    assert origin is None  # Should return None if any component is None

    # Test vol_rot_delta property with None values
    dose_grid.vol_rot_delta_x = 0.1
    dose_grid.vol_rot_delta_y = None
    dose_grid.vol_rot_delta_z = 0.3

    vol_rot_delta = dose_grid.vol_rot_delta
    assert vol_rot_delta is None  # Should return None if any component is None


def test_edge_cases_and_boundary_conditions():
    """Test edge cases and boundary conditions."""
    dose_grid = DoseGrid()

    # Test with small positive dimensions (Dimension class requires positive values)
    dose_grid.dimension = Dimension(1, 1, 1)
    assert dose_grid.dimension_x == 1
    assert dose_grid.dimension_y == 1
    assert dose_grid.dimension_z == 1

    # Test with negative voxel sizes (should be allowed)
    dose_grid.voxel_size = VoxelSize(-1.0, -2.0, -3.0)
    assert dose_grid.voxel_size_x == -1.0
    assert dose_grid.voxel_size_y == -2.0
    assert dose_grid.voxel_size_z == -3.0

    # Test with very large values
    dose_grid.dimension = Dimension(999999, 999999, 999999)
    assert dose_grid.dimension_x == 999999
    assert dose_grid.dimension_y == 999999
    assert dose_grid.dimension_z == 999999

    # Test with very small float values
    dose_grid.voxel_size = VoxelSize(1e-10, 1e-10, 1e-10)
    assert dose_grid.voxel_size_x == 1e-10
    assert dose_grid.voxel_size_y == 1e-10
    assert dose_grid.voxel_size_z == 1e-10

    # Test volume calculation with very small values
    volume = dose_grid.get_voxel_volume()
    assert volume == 1e-30  # 1e-10 * 1e-10 * 1e-10

    # Test with zero dimensions using direct assignment (bypassing Dimension validation)
    dose_grid.dimension_x = 0
    dose_grid.dimension_y = 0
    dose_grid.dimension_z = 0
    assert dose_grid.dimension_x == 0
    assert dose_grid.dimension_y == 0
    assert dose_grid.dimension_z == 0
